#!/usr/bin/env python3
"""
测试空标题和标题顺序问题的修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import CompleteReportGenerator
from pathlib import Path

def test_framework_structure_fixes():
    """测试框架结构修复功能"""
    
    # 创建生成器实例
    generator = CompleteReportGenerator()
    
    # 模拟有问题的框架数据
    problematic_framework = {
        "sections": [
            {
                "title": "第一章 概述",
                "level": 1,
                "content": "这是第一章的内容",
                "children": [
                    {
                        "title": "",  # 空标题问题
                        "level": 2,
                        "content": "",
                        "children": []
                    },
                    {
                        "title": "1.1 子章节",
                        "level": 3,  # 标题顺序问题：应该是level 2
                        "content": "这是子章节内容",
                        "children": []
                    }
                ]
            },
            {
                "title": "第二章 分析",
                "level": 1,
                "content": "",  # 空内容
                "children": [
                    {
                        "title": "2.1 详细分析",
                        "level": 2,
                        "content": "",  # 空内容
                        "children": []
                    }
                ]
            },
            {
                "title": "",  # 空标题
                "level": 1,
                "content": "",
                "children": []
            }
        ]
    }
    
    print("🧪 测试框架结构修复功能")
    print("=" * 60)
    print("📝 原始框架问题:")
    print(f"   - 总章节数: {len(problematic_framework['sections'])}")
    
    # 统计问题
    empty_titles = 0
    empty_contents = 0
    wrong_levels = 0
    
    def count_issues(sections, expected_level=1):
        nonlocal empty_titles, empty_contents, wrong_levels
        for section in sections:
            if not section.get("title", "").strip():
                empty_titles += 1
            if not section.get("content", "").strip() and not section.get("children", []):
                empty_contents += 1
            if section.get("level", 0) != expected_level:
                wrong_levels += 1
            
            if "children" in section:
                count_issues(section["children"], expected_level + 1)
    
    count_issues(problematic_framework["sections"])
    
    print(f"   - 空标题数量: {empty_titles}")
    print(f"   - 空内容数量: {empty_contents}")
    print(f"   - 错误层级数量: {wrong_levels}")
    print()
    
    # 执行修复
    print("🔧 执行框架结构修复...")
    fixed_framework = generator._validate_and_fix_framework_structure(problematic_framework)
    
    # 验证修复结果
    print("✅ 修复后框架状态:")
    print(f"   - 总章节数: {len(fixed_framework['sections'])}")
    
    # 重新统计问题
    empty_titles = 0
    empty_contents = 0
    wrong_levels = 0
    count_issues(fixed_framework["sections"])
    
    print(f"   - 空标题数量: {empty_titles}")
    print(f"   - 空内容数量: {empty_contents}")
    print(f"   - 错误层级数量: {wrong_levels}")
    print()
    
    # 测试Markdown生成
    print("📝 测试Markdown生成...")
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    md_path = generator._generate_markdown(
        "测试报告", 
        fixed_framework, 
        output_dir, 
        "test"
    )
    
    if md_path and Path(md_path).exists():
        print(f"✅ Markdown文档生成成功: {md_path}")
        
        # 检查生成的内容
        with open(md_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 验证是否还有空标题
        lines = content.split('\n')
        empty_headers = [line for line in lines if line.strip().startswith('#') and len(line.strip()) <= 10]
        
        print(f"   - 文档总行数: {len(lines)}")
        print(f"   - 空标题数量: {len(empty_headers)}")
        
        if empty_headers:
            print("   ⚠️ 仍存在空标题:")
            for header in empty_headers[:3]:
                print(f"     - '{header.strip()}'")
        else:
            print("   ✅ 无空标题问题")
        
        # 检查标题层级
        header_levels = []
        for line in lines:
            if line.strip().startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                header_levels.append(level)
        
        # 检查层级是否连续
        level_issues = 0
        for i in range(1, len(header_levels)):
            if header_levels[i] > header_levels[i-1] + 1:
                level_issues += 1
        
        print(f"   - 标题层级跳跃问题: {level_issues}")
        
        if level_issues == 0:
            print("   ✅ 标题层级正常")
        else:
            print("   ⚠️ 仍存在标题层级问题")
    else:
        print("❌ Markdown文档生成失败")
    
    print()
    print("🎯 测试总结:")
    success = empty_titles == 0 and empty_contents == 0 and wrong_levels == 0
    if success:
        print("✅ 所有问题已修复")
    else:
        print("⚠️ 部分问题仍需解决")
    
    return success

def test_markdown_generation():
    """测试Markdown生成的具体问题"""
    
    generator = CompleteReportGenerator()
    
    # 创建测试框架
    test_framework = {
        "sections": [
            {
                "title": "1. 总论：地热发电产业概览与研究界定",
                "level": 1,
                "content": "地热能作为清洁可再生能源，具有重要的战略价值。",
                "children": [
                    {
                        "title": "1.1. 产业核心概念与价值剖析",
                        "level": 2,
                        "content": "",  # 空内容但有子节点
                        "children": [
                            {
                                "title": "1.1.1. 地热能与地热发电基本定义",
                                "level": 3,
                                "content": "",  # 空内容且无子节点
                                "children": []
                            },
                            {
                                "title": "1.1.2. 地热资源类型与全球分布特征",
                                "level": 3,
                                "content": "地热资源的禀赋特征直接决定了其开发的技术路线。",
                                "children": []
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    print("📝 测试Markdown生成功能")
    print("=" * 60)
    
    # 生成Markdown
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    md_path = generator._generate_markdown(
        "地热发电产业研究报告", 
        test_framework, 
        output_dir, 
        "20250805_test"
    )
    
    if md_path and Path(md_path).exists():
        print(f"✅ 测试文档生成: {md_path}")
        
        with open(md_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📄 生成的内容:")
        print("-" * 40)
        print(content)
        print("-" * 40)
        
        # 分析问题
        lines = content.split('\n')
        issues = []
        
        for i, line in enumerate(lines, 1):
            if line.strip().startswith('#'):
                # 检查是否是空标题
                title_text = line.strip().lstrip('#').strip()
                if not title_text:
                    issues.append(f"第{i}行: 空标题")
                
                # 检查下一行是否有内容
                if i < len(lines):
                    next_line = lines[i].strip() if i < len(lines) else ""
                    if not next_line and (i + 1 >= len(lines) or not lines[i + 1].strip()):
                        issues.append(f"第{i}行: 标题'{title_text}'下无内容")
        
        if issues:
            print("⚠️ 发现问题:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print("✅ 无明显问题")
    
    return len(issues) == 0 if 'issues' in locals() else False

if __name__ == "__main__":
    print("🧪 开始测试BUG修复效果\n")
    
    # 测试1：框架结构修复
    test1_success = test_framework_structure_fixes()
    print()
    
    # 测试2：Markdown生成
    test2_success = test_markdown_generation()
    print()
    
    # 总结
    print("🎉 测试完成")
    if test1_success and test2_success:
        print("✅ 所有测试通过，BUG修复成功")
        sys.exit(0)
    else:
        print("⚠️ 部分测试失败，需要进一步优化")
        sys.exit(1)
