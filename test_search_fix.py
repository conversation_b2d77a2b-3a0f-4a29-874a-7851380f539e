#!/usr/bin/env python3
"""
测试联网搜索功能修复
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def test_search_functionality():
    """测试搜索功能"""
    print("🔧 测试联网搜索功能修复...")
    
    try:
        # 初始化报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试搜索功能
        search_works = generator.test_search_functionality()
        
        if search_works:
            print("\n✅ 搜索功能测试通过！")
            
            # 测试搜索增强流程
            print("\n🔍 测试搜索增强流程...")
            
            # 创建一个简单的测试报告
            test_content = """
# 人工智能发展报告

## 1. 概述
人工智能技术正在快速发展。

## 2. 技术趋势
机器学习和深度学习是主要方向。

## 3. 市场分析
AI市场规模持续增长。
"""
            
            # 保存测试报告
            test_report_path = "test_report.md"
            with open(test_report_path, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            # 测试搜索增强
            try:
                enhanced_path = generator.enhance_report_with_tool_calling(
                    test_report_path, 
                    "人工智能发展", 
                    user_confirm=False  # 跳过用户确认
                )
                
                if enhanced_path != test_report_path:
                    print(f"✅ 搜索增强成功！增强报告: {enhanced_path}")
                    
                    # 显示增强后的内容片段
                    if os.path.exists(enhanced_path):
                        with open(enhanced_path, 'r', encoding='utf-8') as f:
                            enhanced_content = f.read()
                        
                        print(f"\n📄 增强后的报告长度: {len(enhanced_content)} 字符")
                        if len(enhanced_content) > len(test_content):
                            print("✅ 报告内容已被成功增强！")
                        else:
                            print("⚠️ 报告内容未明显增强")
                    
                    return True
                else:
                    print("⚠️ 搜索增强未执行")
                    return False
                    
            except Exception as e:
                print(f"❌ 搜索增强测试失败: {str(e)}")
                return False
            
            finally:
                # 清理测试文件
                for file_path in [test_report_path, test_report_path.replace('.md', '_search_enhanced.md')]:
                    if os.path.exists(file_path):
                        try:
                            os.remove(file_path)
                            print(f"🗑️ 已清理测试文件: {file_path}")
                        except:
                            pass
        else:
            print("\n❌ 搜索功能测试失败！")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 联网搜索功能修复测试")
    print("=" * 60)
    
    success = test_search_functionality()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 联网搜索功能修复成功！")
        print("🎉 现在可以正常使用搜索增强功能了")
    else:
        print("❌ 联网搜索功能仍有问题")
        print("💡 请检查网络连接和API配置")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
